use crate::models::config::MonitorConfig;
use crate::services::{
    account::{AccountService, WindowsAccountService},
    auth::AuthService,
    browser::{BrowserService, WindowsBrowserService},
    config::ConfigService,
    logging::LoggingService,
    monitor::MonitorService,
    notification::NotificationService,
};
use tauri::AppHandle;

/// 闲鱼业务逻辑处理器 - 协调各个服务完成业务功能
#[derive(Clone)]
pub struct GoldfishBusiness {
    app_handle: AppHandle,
    logging_service: LoggingService,
    config_service: ConfigService,
    auth_service: AuthService,
    browser_service: BrowserService,
    monitor_service: MonitorService,
    notification_service: NotificationService,
    account_service: AccountService,
    windows_browser_service: Option<WindowsBrowserService>, // Windows 专用浏览器服务
}

impl GoldfishBusiness {
    /// 创建新的业务处理器
    pub fn new(
        app_handle: AppHandle,
        logging_service: LoggingService,
        config_service: ConfigService,
        auth_service: AuthService,
        browser_service: BrowserService,
        monitor_service: MonitorService,
        notification_service: NotificationService,
        account_service: AccountService,
    ) -> Self {
        // 检测操作系统，如果是 Windows 则创建 Windows 专用浏览器服务
        let windows_browser_service = if cfg!(target_os = "windows") {
            println!("🪟 检测到 Windows 平台，初始化 Windows 专用浏览器服务");
            Some(WindowsBrowserService::new(app_handle.clone()))
        } else {
            println!("🍎 检测到非 Windows 平台，使用标准浏览器服务");
            None
        };

        Self {
            app_handle,
            logging_service,
            config_service,
            auth_service,
            browser_service,
            monitor_service,
            notification_service,
            account_service,
            windows_browser_service,
        }
    }

    /// 启动闲鱼监控业务流程
    pub async fn start_goldfish_monitoring(&self) -> Result<(), String> {
        // 1. 记录业务日志
        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "start_goldfish_monitoring",
                "开始启动闲鱼监控",
            )
            .await?;

        // 2. 检查激活状态
        if !self.auth_service.check_activation_status().await? {
            return Err("应用未激活，无法启动监控".to_string());
        }

        // 3. 验证所有启用账号的Cookie有效性
        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "start_goldfish_monitoring",
                "开始验证账号Cookie有效性",
            )
            .await?;

        if let Err(e) = self.account_service.validate_all_accounts().await {
            return Err(format!("账号验证失败: {}", e));
        }

        // 4. 获取配置
        let config = self.config_service.get_config().await;

        // 5. 设置通知渠道
        self.setup_notification_channels(&config).await?;

        // 6. 启动监控
        self.monitor_service.start_monitoring(config).await?;

        // 7. 记录成功日志
        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "start_goldfish_monitoring",
                "闲鱼监控启动成功",
            )
            .await?;

        Ok(())
    }

    /// 停止闲鱼监控
    pub async fn stop_goldfish_monitoring(&self) -> Result<(), String> {
        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "stop_goldfish_monitoring",
                "停止闲鱼监控",
            )
            .await?;

        self.monitor_service.stop_monitoring().await?;

        Ok(())
    }

    /// 处理闲鱼登录流程
    pub async fn handle_goldfish_login(&self) -> Result<(), String> {
        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "handle_goldfish_login",
                "开始闲鱼登录流程",
            )
            .await?;

        // 1. 打开闲鱼登录页面
        let login_url = "https://www.goofish.com/search?q=相机";
        let window_config = crate::services::browser::BrowserWindowConfig::default()
            .with_title("闲鱼登录 - 请完成登录后关闭窗口")
            .with_size(1200.0, 800.0);

        // 打开浏览器窗口并获取实例
        let (browser_window, _data_store_identifier) = self
            .browser_service
            .open_browser(
                login_url,
                Some("goofish_login".to_string()),
                Some(window_config),
            )
            .await?;

        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "handle_goldfish_login",
                &format!("已打开登录窗口: {}", browser_window.label()),
            )
            .await?;

        // 延迟注册窗口关闭回调，确保窗口完全加载
        let browser_window_for_callback = browser_window.clone();
        let self_clone = self.clone();
        tokio::spawn(async move {
            // 等待窗口完全加载
            tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;

            if let Err(e) = self_clone
                .register_login_window_callbacks(&browser_window_for_callback)
                .await
            {
                eprintln!("注册窗口回调失败: {}", e);
            }
        });

        Ok(())
    }

    /// 注册登录窗口的回调到回调池
    async fn register_login_window_callbacks(
        &self,
        browser_window: &crate::services::browser::BrowserWindow,
    ) -> Result<(), String> {
        let window_label = browser_window.label();
        let config_service = self.config_service.clone();
        let logging_service = self.logging_service.clone();
        let account_service = self.account_service.clone();

        // 注册关闭请求回调 - 明确指定要阻止关闭
        crate::utils::add_close_requested_callback(
            window_label,
            move |window| {
                let config_service = config_service.clone();
                let logging_service = logging_service.clone();
                let account_service = account_service.clone();
                let window = window.clone();

                Box::pin(async move {
                    // 严格检查：只处理咸鱼登录窗口
                    if window.label() != "goofish_login" {
                        return Ok(crate::utils::CallbackResult::Continue);
                    }

                    if let Err(e) = logging_service
                        .business_info(
                            "GoldfishBusiness",
                            "close_requested",
                            &format!(
                                "咸鱼登录窗口 {} 请求关闭，阻止关闭并开始提取Cookie",
                                window.label()
                            ),
                        )
                        .await
                    {
                        eprintln!("日志记录失败: {}", e);
                    }

                    // 直接获取 Cookie
                    let cookies_result = window.cookies();

                    match cookies_result {
                        Ok(cookies) => {
                            if let Err(e) = logging_service
                                .business_success(
                                    "GoldfishBusiness",
                                    "close_requested",
                                    &format!("成功提取了 {} 个Cookie", cookies.len()),
                                )
                                .await
                            {
                                eprintln!("日志记录失败: {}", e);
                            }

                            // 保存所有Cookie
                            let all_cookies: Vec<_> = cookies
                                .into_iter()
                                .map(|cookie| format!("{}={}", cookie.name(), cookie.value()))
                                .collect();

                            if !all_cookies.is_empty() {
                                let cookie_string = all_cookies.join("; ");

                                if let Err(e) = logging_service
                                    .business_success(
                                        "GoldfishBusiness",
                                        "close_requested",
                                        &format!(
                                            "成功提取了 {} 个Cookie，开始自动添加账号",
                                            all_cookies.len()
                                        ),
                                    )
                                    .await
                                {
                                    eprintln!("日志记录失败: {}", e);
                                }

                                // 异步处理账号添加
                                let account_service_clone = account_service.clone();
                                let cookie_string_clone = cookie_string.clone();
                                tokio::spawn(async move {
                                    // 验证Cookie并获取用户信息
                                    match account_service_clone
                                        .get_user_info(&cookie_string_clone)
                                        .await
                                    {
                                        Ok(user_info) if user_info.is_valid => {
                                            println!("✅ Cookie验证成功，自动添加账号");

                                            // 添加账号
                                            match account_service_clone
                                                .add_account_with_user_info(
                                                    user_info
                                                        .nickname
                                                        .clone()
                                                        .unwrap_or_else(|| "未知用户".to_string()),
                                                    user_info.user_id.clone(),
                                                    cookie_string_clone,
                                                    Some("通过登录窗口自动添加".to_string()),
                                                )
                                                .await
                                            {
                                                Ok((account, is_update)) => {
                                                    let message = if is_update {
                                                        format!(
                                                            "账号 {} 已更新",
                                                            user_info.nickname.unwrap_or_else(
                                                                || "未知用户".to_string()
                                                            )
                                                        )
                                                    } else {
                                                        format!(
                                                            "账号 {} 已添加",
                                                            user_info.nickname.unwrap_or_else(
                                                                || "未知用户".to_string()
                                                            )
                                                        )
                                                    };
                                                    println!("✅ 自动添加账号成功: {}", message);
                                                }
                                                Err(e) => {
                                                    eprintln!("❌ 自动添加账号失败: {}", e);
                                                }
                                            }
                                        }
                                        Ok(_) => {
                                            eprintln!("❌ Cookie验证失败");
                                        }
                                        Err(e) => {
                                            eprintln!("❌ Cookie验证出错: {}", e);
                                        }
                                    }
                                });
                            } else {
                                if let Err(e) = logging_service
                                    .business_info(
                                        "GoldfishBusiness",
                                        "close_requested",
                                        "未找到任何Cookie",
                                    )
                                    .await
                                {
                                    eprintln!("日志记录失败: {}", e);
                                }
                            }
                        }
                        Err(e) => {
                            if let Err(log_err) = logging_service
                                .business_info(
                                    "GoldfishBusiness",
                                    "close_requested",
                                    &format!("Cookie API 调用失败: {}", e),
                                )
                                .await
                            {
                                eprintln!("日志记录失败: {}", log_err);
                            }
                        }
                    }

                    // 异步处理完成后手动关闭窗口
                    if let Err(e) = window.close() {
                        eprintln!("手动关闭窗口失败: {}", e);
                    }

                    // 返回 PreventAndHandle 表示我们阻止了默认关闭并自行处理
                    Ok(crate::utils::CallbackResult::PreventAndHandle)
                })
            },
            true, // 阻止关闭
            "咸鱼登录窗口Cookie提取回调",
        )
        .await;

        // 注册窗口关闭后的清理回调
        let logging_service_clone = self.logging_service.clone();
        crate::utils::add_closed_callback(
            window_label,
            move |window| {
                let logging_service = logging_service_clone.clone();
                let window_label = window.label().to_string();

                Box::pin(async move {
                    if let Err(e) = logging_service
                        .business_info(
                            "GoldfishBusiness",
                            "closed",
                            &format!("窗口 {} 已关闭，清理回调", window_label),
                        )
                        .await
                    {
                        eprintln!("日志记录失败: {}", e);
                    }

                    // 移除该窗口的所有回调
                    crate::utils::remove_window_callbacks(&window_label).await;
                    Ok(crate::utils::CallbackResult::Continue)
                })
            },
            "咸鱼登录窗口清理回调",
        )
        .await;

        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "register_callbacks",
                &format!("已为窗口 {} 注册回调", window_label),
            )
            .await?;
        Ok(())
    }

    /// 设置通知渠道
    async fn setup_notification_channels(&self, config: &MonitorConfig) -> Result<(), String> {
        // 设置钉钉通知渠道（仅在开关开启时）
        if config.dingtalk_enabled {
            for (index, hook_url) in config.dingtalk_hooks.iter().enumerate() {
                let channel_name = format!("dingtalk_{}", index);
                let channel = crate::services::notification::NotificationChannel::DingTalk {
                    webhook_url: hook_url.clone(),
                };

                self.notification_service
                    .add_channel(channel_name, channel)
                    .await?;
            }
        }

        // 添加系统通知渠道
        let system_channel = crate::services::notification::NotificationChannel::System;
        self.notification_service
            .add_channel("system".to_string(), system_channel)
            .await?;

        Ok(())
    }

    /// 发送新商品通知
    pub async fn send_new_item_notification(
        &self,
        item: &crate::models::config::MonitorItem,
    ) -> Result<(), String> {
        let message = crate::services::notification::NotificationMessage {
            title: "发现新商品".to_string(),
            content: format!(
                "商品: {}\n价格: {}\n卖家: {}",
                item.title, item.price, item.user_nick_name
            ),
            level: crate::services::notification::service::NotificationLevel::Info,
            timestamp: chrono::Utc::now().to_rfc3339(),
            metadata: std::collections::HashMap::new(),
        };

        self.notification_service
            .broadcast_notification(message)
            .await?;

        Ok(())
    }

    /// 获取业务状态
    pub async fn get_business_status(&self) -> Result<serde_json::Value, String> {
        let monitor_status = self.monitor_service.get_status().await;
        let activation_info = self.auth_service.get_activation_info().await;
        let config = self.config_service.get_config().await;

        Ok(serde_json::json!({
            "monitor": monitor_status,
            "activation": activation_info,
            "has_cookie": config.has_cookie(),
            "timestamp": chrono::Utc::now().to_rfc3339()
        }))
    }

    /// 处理带ID的闲鱼登录流程（用于自动添加账号）
    pub async fn handle_goldfish_login_with_id(
        &self,
        login_id: &str,
        description: Option<String>,
    ) -> Result<(), String> {
        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "handle_goldfish_login_with_id",
                &format!("开始带ID的闲鱼登录流程: login_id={}", login_id),
            )
            .await?;

        // 平台检测：Windows 使用专用流程
        if self.is_windows_platform() {
            return self
                .handle_goldfish_login_with_id_windows(login_id, description)
                .await;
        }

        // macOS/Linux 使用原有流程
        self.handle_goldfish_login_with_id_standard(login_id, description)
            .await
    }

    /// Windows 专用：处理带ID的闲鱼登录流程
    async fn handle_goldfish_login_with_id_windows(
        &self,
        login_id: &str,
        description: Option<String>,
    ) -> Result<(), String> {
        println!(
            "🪟 Windows 专用：开始带ID的闲鱼登录流程: login_id={}",
            login_id
        );

        let windows_browser_service = self
            .get_windows_browser_service()
            .ok_or("Windows 专用浏览器服务未初始化")?;

        // 1. 保存描述用于窗口标题
        let window_title = description.as_deref().unwrap_or("新账号").to_string();

        // 2. 通过账号服务创建 Windows 专用登录会话
        let windows_service = self
            .account_service
            .get_windows_service()
            .ok_or("Windows 专用账号服务未初始化")?;

        let session_id = windows_service
            .create_windows_login_session(description)
            .await?;

        // 3. 获取 Windows 会话信息
        let session = windows_service
            .get_windows_login_session(&session_id)
            .await
            .ok_or_else(|| "无法获取 Windows 登录会话".to_string())?;

        // 4. 打开闲鱼登录页面，使用 Windows 专用浏览器
        let login_url = "https://www.goofish.com/search?q=相机";

        let window_config = crate::services::browser::BrowserWindowConfig::default()
            .with_title(&format!(
                "闲鱼登录 - {} - 请完成登录后关闭窗口",
                window_title
            ))
            .with_size(1200.0, 800.0);

        // 使用 Windows 专用浏览器服务打开窗口
        let browser_window = windows_browser_service
            .open_windows_browser(login_url, &session, Some(window_config))
            .await?;

        println!("✅ Windows 专用登录窗口已打开: {}", browser_window.label());

        // 5. 注册 Windows 专用窗口关闭回调
        self.register_windows_login_session_window_callbacks(&browser_window, &session_id)
            .await?;

        Ok(())
    }

    /// 标准平台：处理带ID的闲鱼登录流程（原有逻辑）
    async fn handle_goldfish_login_with_id_standard(
        &self,
        login_id: &str,
        description: Option<String>,
    ) -> Result<(), String> {
        println!("🍎 标准平台：开始带ID的闲鱼登录流程: login_id={}", login_id);

        // 1. 保存描述用于窗口标题
        let window_title = description.as_deref().unwrap_or("新账号").to_string();

        // 2. 创建登录会话
        let session_id = self
            .account_service
            .create_login_session(description)
            .await?;

        // 3. 获取会话信息
        let session = self
            .account_service
            .get_login_session(&session_id)
            .await
            .ok_or_else(|| "无法获取登录会话".to_string())?;

        // 4. 打开闲鱼登录页面，使用独立的用户数据目录
        let login_url = "https://www.goofish.com/search?q=相机";
        let user_data_path = session.data_directory.to_string_lossy().to_string();

        // 会话数据目录已设置

        let window_config = crate::services::browser::BrowserWindowConfig::default()
            .with_title(&format!(
                "闲鱼登录 - {} - 请完成登录后关闭窗口",
                window_title
            ))
            .with_size(1200.0, 800.0)
            .with_user_data_path(user_data_path.clone());

        // 窗口配置已设置

        // 使用session_id作为窗口标识
        let window_label = format!("goofish_login_{}", session_id);

        // 4. 打开浏览器窗口并获取实例
        let (browser_window, _data_store_identifier) = self
            .browser_service
            .open_browser(login_url, Some(window_label.clone()), Some(window_config))
            .await?;

        // 注意：identifier已经在create_login_session时生成并保存了

        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "handle_goldfish_login_with_id",
                &format!(
                    "已打开登录窗口: {} (会话ID: {})",
                    browser_window.label(),
                    session_id
                ),
            )
            .await?;

        // 延迟注册窗口关闭回调，确保窗口完全加载
        let browser_window_for_callback = browser_window.clone();
        let self_clone = self.clone();
        let session_id_clone = session_id.clone();
        tokio::spawn(async move {
            // 等待窗口完全加载
            tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;

            if let Err(e) = self_clone
                .register_login_session_window_callbacks(
                    &browser_window_for_callback,
                    &session_id_clone,
                )
                .await
            {
                eprintln!("注册会话窗口回调失败: {}", e);
            }
        });

        Ok(())
    }

    /// 注册登录会话窗口的关闭回调
    async fn register_login_session_window_callbacks(
        &self,
        browser_window: &crate::services::browser::BrowserWindow,
        session_id: &str,
    ) -> Result<(), String> {
        let window_label = browser_window.label();
        let account_service = self.account_service.clone();
        let logging_service = self.logging_service.clone();
        let session_id = session_id.to_string();

        // 注册关闭请求回调
        crate::utils::add_close_requested_callback(
            window_label,
            move |window| {
                let account_service = account_service.clone();
                let logging_service = logging_service.clone();
                let session_id = session_id.clone();
                let window = window.clone();

                Box::pin(async move {
                    // 检查是否是登录会话窗口
                    if !window.label().starts_with("goofish_login_") {
                        return Ok(crate::utils::CallbackResult::Continue);
                    }

                    if let Err(e) = logging_service
                        .business_info(
                            "GoldfishBusiness",
                            "session_close_requested",
                            &format!(
                                "登录会话窗口 {} 请求关闭，开始提取Cookie (会话ID: {})",
                                window.label(),
                                session_id
                            ),
                        )
                        .await
                    {
                        eprintln!("日志记录失败: {}", e);
                    }

                    // 提取Cookie
                    let cookies_result = window.cookies();

                    match cookies_result {
                        Ok(cookies) => {
                            if let Err(e) = logging_service
                                .business_success(
                                    "GoldfishBusiness",
                                    "session_close_requested",
                                    &format!("成功提取了 {} 个Cookie", cookies.len()),
                                )
                                .await
                            {
                                eprintln!("日志记录失败: {}", e);
                            }

                            // 保存所有Cookie
                            let all_cookies: Vec<_> = cookies
                                .into_iter()
                                .map(|cookie| format!("{}={}", cookie.name(), cookie.value()))
                                .collect();

                            if !all_cookies.is_empty() {
                                let cookie_string = all_cookies.join("; ");

                                if let Err(e) = logging_service
                                    .business_success(
                                        "GoldfishBusiness",
                                        "session_close_requested",
                                        &format!(
                                            "成功提取了 {} 个Cookie，开始处理会话",
                                            all_cookies.len()
                                        ),
                                    )
                                    .await
                                {
                                    eprintln!("日志记录失败: {}", e);
                                }

                                // 异步处理登录会话完成
                                let account_service_clone = account_service.clone();
                                let session_id_clone = session_id.clone();
                                let cookie_string_clone = cookie_string.clone();
                                tokio::spawn(async move {
                                    if let Err(e) = account_service_clone
                                        .handle_login_session_completed_with_cookie(
                                            &session_id_clone,
                                            cookie_string_clone,
                                        )
                                        .await
                                    {
                                        eprintln!("处理登录会话完成失败: {}", e);
                                    }
                                });
                            } else {
                                if let Err(e) = logging_service
                                    .business_info(
                                        "GoldfishBusiness",
                                        "session_close_requested",
                                        "未找到任何Cookie，清理会话",
                                    )
                                    .await
                                {
                                    eprintln!("日志记录失败: {}", e);
                                }

                                // 没有找到Cookie，清理会话
                                let account_service_clone = account_service.clone();
                                let session_id_clone = session_id.clone();
                                tokio::spawn(async move {
                                    if let Err(e) = account_service_clone
                                        .cleanup_login_session(&session_id_clone)
                                        .await
                                    {
                                        eprintln!("清理登录会话失败: {}", e);
                                    }
                                });
                            }
                        }
                        Err(e) => {
                            if let Err(log_err) = logging_service
                                .business_info(
                                    "GoldfishBusiness",
                                    "session_close_requested",
                                    &format!("Cookie API 调用失败: {}，清理会话", e),
                                )
                                .await
                            {
                                eprintln!("日志记录失败: {}", log_err);
                            }

                            // Cookie提取失败，清理会话
                            let account_service_clone = account_service.clone();
                            let session_id_clone = session_id.clone();
                            tokio::spawn(async move {
                                if let Err(e) = account_service_clone
                                    .cleanup_login_session(&session_id_clone)
                                    .await
                                {
                                    eprintln!("清理登录会话失败: {}", e);
                                }
                            });
                        }
                    }

                    // 异步处理完成后手动关闭窗口
                    if let Err(e) = window.close() {
                        eprintln!("手动关闭窗口失败: {}", e);
                    }

                    Ok(crate::utils::CallbackResult::PreventAndHandle)
                })
            },
            true, // 阻止关闭
            "登录会话窗口Cookie提取回调",
        )
        .await;

        println!("✅ 已注册登录会话窗口关闭回调: {}", window_label);
        Ok(())
    }

    /// 更新指定账号的Cookie
    pub async fn update_account_cookie(&self, account_id: &str) -> Result<(), String> {
        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "update_account_cookie",
                &format!("开始更新账号Cookie: {}", account_id),
            )
            .await?;

        // 1. 获取账号信息
        let account = self
            .account_service
            .get_account(account_id)
            .await
            .ok_or_else(|| format!("账号不存在: {}", account_id))?;

        // 2. 重用或创建登录会话
        let session_id = self
            .account_service
            .reuse_login_session_for_account(account_id)
            .await?;

        // 3. 获取会话信息
        let session = self
            .account_service
            .get_login_session(&session_id)
            .await
            .ok_or_else(|| "无法获取登录会话".to_string())?;

        // 4. 打开闲鱼登录页面，使用会话的用户数据目录
        let login_url = "https://www.goofish.com/search?q=相机";
        let user_data_path = session.data_directory.to_string_lossy().to_string();

        let window_config = crate::services::browser::BrowserWindowConfig::default()
            .with_title(&format!(
                "更新Cookie - {} - 请完成登录后关闭窗口",
                account.name
            ))
            .with_size(1200.0, 800.0)
            .with_user_data_path(user_data_path.clone());

        // 更新Cookie窗口配置已设置

        // 使用session_id作为窗口标识
        let window_label = format!("goofish_update_{}", session_id);

        // 4. 打开浏览器窗口
        let (browser_window, _data_store_identifier) = self
            .browser_service
            .open_browser(login_url, Some(window_label.clone()), Some(window_config))
            .await?;

        // 注意：identifier已经在会话创建/重用时生成并保存了

        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "update_account_cookie",
                &format!(
                    "已打开更新Cookie窗口: {} (会话ID: {})",
                    browser_window.label(),
                    session_id
                ),
            )
            .await?;

        // 5. 延迟注册窗口关闭回调
        let browser_window_for_callback = browser_window.clone();
        let self_clone = self.clone();
        let session_id_clone = session_id.clone();
        let account_id_clone = account_id.to_string();
        tokio::spawn(async move {
            // 等待窗口完全加载
            tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;

            if let Err(e) = self_clone
                .register_update_cookie_window_callbacks(
                    &browser_window_for_callback,
                    &session_id_clone,
                    &account_id_clone,
                )
                .await
            {
                eprintln!("注册更新Cookie窗口回调失败: {}", e);
            }
        });

        Ok(())
    }

    /// 注册更新Cookie窗口的关闭回调
    async fn register_update_cookie_window_callbacks(
        &self,
        browser_window: &crate::services::browser::BrowserWindow,
        session_id: &str,
        account_id: &str,
    ) -> Result<(), String> {
        let window_label = browser_window.label();
        let account_service = self.account_service.clone();
        let logging_service = self.logging_service.clone();
        let session_id = session_id.to_string();
        let account_id = account_id.to_string();

        // 注册关闭请求回调
        crate::utils::add_close_requested_callback(
            window_label,
            move |window| {
                let account_service = account_service.clone();
                let logging_service = logging_service.clone();
                let session_id = session_id.clone();
                let account_id = account_id.clone();
                let window = window.clone();

                Box::pin(async move {
                    // 检查是否是更新Cookie窗口
                    if !window.label().starts_with("goofish_update_") {
                        return Ok(crate::utils::CallbackResult::Continue);
                    }

                    if let Err(e) = logging_service
                        .business_info(
                            "GoldfishBusiness",
                            "update_cookie_close_requested",
                            &format!(
                                "更新Cookie窗口 {} 请求关闭，开始提取Cookie (会话ID: {}, 账号ID: {})",
                                window.label(),
                                session_id,
                                account_id
                            ),
                        )
                        .await
                    {
                        eprintln!("日志记录失败: {}", e);
                    }

                    // 提取Cookie
                    let cookies_result = window.cookies();

                    match cookies_result {
                        Ok(cookies) => {
                            if let Err(e) = logging_service
                                .business_success(
                                    "GoldfishBusiness",
                                    "update_cookie_close_requested",
                                    &format!("成功提取了 {} 个Cookie", cookies.len()),
                                )
                                .await
                            {
                                eprintln!("日志记录失败: {}", e);
                            }

                            // 保存所有Cookie
                            let all_cookies: Vec<_> = cookies
                                .into_iter()
                                .map(|cookie| format!("{}={}", cookie.name(), cookie.value()))
                                .collect();

                            if !all_cookies.is_empty() {
                                let cookie_string = all_cookies.join("; ");

                                if let Err(e) = logging_service
                                    .business_success(
                                        "GoldfishBusiness",
                                        "update_cookie_close_requested",
                                        &format!(
                                            "成功提取了 {} 个Cookie，开始更新账号",
                                            all_cookies.len()
                                        ),
                                    )
                                    .await
                                {
                                    eprintln!("日志记录失败: {}", e);
                                }

                                // 异步更新账号Cookie
                                let account_service_clone = account_service.clone();
                                let session_id_clone = session_id.clone();
                                let account_id_clone = account_id.clone();
                                let cookie_string_clone = cookie_string.clone();
                                tokio::spawn(async move {
                                    if let Err(e) = account_service_clone
                                        .update_account_cookie_with_session(
                                            &account_id_clone,
                                            &session_id_clone,
                                            cookie_string_clone,
                                        )
                                        .await
                                    {
                                        eprintln!("更新账号Cookie失败: {}", e);
                                    }
                                });
                            } else {
                                if let Err(e) = logging_service
                                    .business_info(
                                        "GoldfishBusiness",
                                        "update_cookie_close_requested",
                                        "未找到任何Cookie，清理会话",
                                    )
                                    .await
                                {
                                    eprintln!("日志记录失败: {}", e);
                                }

                                // 没有找到Cookie，但不清理会话（保持会话ID稳定）
                                println!("⚠️ 未找到Cookie，但保留会话以便下次重用");
                            }
                        }
                        Err(e) => {
                            if let Err(log_err) = logging_service
                                .business_info(
                                    "GoldfishBusiness",
                                    "update_cookie_close_requested",
                                    &format!("Cookie API 调用失败: {}，清理会话", e),
                                )
                                .await
                            {
                                eprintln!("日志记录失败: {}", log_err);
                            }

                            // Cookie提取失败，但不清理会话（保持会话ID稳定）
                            println!("⚠️ Cookie提取失败，但保留会话以便下次重用");
                        }
                    }

                    // 异步处理完成后手动关闭窗口
                    if let Err(e) = window.close() {
                        eprintln!("手动关闭窗口失败: {}", e);
                    }

                    Ok(crate::utils::CallbackResult::PreventAndHandle)
                })
            },
            true, // 阻止关闭
            "更新Cookie窗口回调",
        )
        .await;

        println!("✅ 已注册更新Cookie窗口关闭回调: {}", window_label);
        Ok(())
    }

    /// 检测是否为 Windows 平台
    fn is_windows_platform(&self) -> bool {
        cfg!(target_os = "windows")
    }

    /// 获取 Windows 专用浏览器服务（如果可用）
    fn get_windows_browser_service(&self) -> Option<&WindowsBrowserService> {
        self.windows_browser_service.as_ref()
    }

    /// Windows 专用：注册登录会话窗口关闭回调
    async fn register_windows_login_session_window_callbacks(
        &self,
        browser_window: &crate::services::browser::BrowserWindow,
        session_id: &str,
    ) -> Result<(), String> {
        let window_label = browser_window.label().to_string();
        println!("🪟 注册 Windows 登录会话窗口关闭回调: {}", window_label);

        let self_clone = self.clone();
        let session_id_clone = session_id.to_string();

        // 注册窗口关闭回调
        let callback_config = crate::utils::window_callback_pool::CallbackConfig {
            prevent_close: true,
            description: "Windows 登录会话窗口回调".to_string(),
            execute_once: false,
        };

        crate::utils::get_window_callback_pool()
            .add_callback(
                &window_label,
                move |_window, _event| {
                    let self_clone = self_clone.clone();
                    let session_id_clone = session_id_clone.clone();
                    Box::pin(async move {
                        println!("🪟 Windows 登录窗口关闭，处理会话: {}", session_id_clone);

                        // 获取 Windows 专用服务
                        if let Some(windows_service) =
                            self_clone.account_service.get_windows_service()
                        {
                            // 这里需要实现从浏览器中提取Cookie的逻辑
                            // 暂时使用模拟的Cookie处理
                            println!("🪟 Windows 登录会话处理完成: {}", session_id_clone);

                            // 清理会话
                            if let Err(e) = windows_service
                                .cleanup_windows_login_session(&session_id_clone)
                                .await
                            {
                                println!("⚠️ 清理 Windows 登录会话失败: {}", e);
                            }
                        }

                        Ok(crate::utils::window_callback_pool::CallbackResult::Continue)
                    })
                },
                callback_config,
            )
            .await;

        println!("✅ 已注册 Windows 登录会话窗口关闭回调: {}", window_label);
        Ok(())
    }

    /// 处理账号验证请求（打开验证窗口）
    pub async fn handle_account_verification(&self, account_id: &str) -> Result<(), String> {
        println!("🔐 处理账号验证请求: account_id={}", account_id);

        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "handle_account_verification",
                &format!("开始处理账号验证: account_id={}", account_id),
            )
            .await?;

        // 获取账号信息
        let account = {
            let config = self.account_service.get_config().await;
            config
                .accounts
                .iter()
                .find(|a| a.id == account_id)
                .cloned()
                .ok_or_else(|| format!("账号不存在: {}", account_id))?
        };

        // 平台检测：Windows 使用专用流程
        if self.is_windows_platform() {
            return self
                .handle_account_verification_windows(account_id, &account)
                .await;
        }

        // macOS/Linux 使用标准流程
        self.handle_account_verification_standard(account_id, &account)
            .await
    }

    /// Windows 专用：处理账号验证
    async fn handle_account_verification_windows(
        &self,
        account_id: &str,
        account: &crate::models::account::Account,
    ) -> Result<(), String> {
        println!("🪟 Windows 专用：处理账号验证: account_id={}", account_id);

        let windows_browser_service = self
            .get_windows_browser_service()
            .ok_or("Windows 专用浏览器服务未初始化")?;

        let windows_service = self
            .account_service
            .get_windows_service()
            .ok_or("Windows 专用账号服务未初始化")?;

        // 重用或创建 Windows 专用登录会话
        let session_id = windows_service
            .reuse_windows_login_session_for_account(account)
            .await?;

        // 获取 Windows 会话信息
        let session = windows_service
            .get_windows_login_session(&session_id)
            .await
            .ok_or_else(|| "无法获取 Windows 登录会话".to_string())?;

        // 打开验证页面
        let verification_url = "https://www.goofish.com/search?q=相机";

        let window_config = crate::services::browser::BrowserWindowConfig::default()
            .with_title(&format!(
                "账号验证 - {} - 请完成验证后关闭窗口",
                account.name
            ))
            .with_size(1200.0, 800.0);

        // 使用 Windows 专用浏览器服务打开窗口
        let browser_window = windows_browser_service
            .open_windows_browser(verification_url, &session, Some(window_config))
            .await?;

        println!("✅ Windows 专用验证窗口已打开: {}", browser_window.label());

        // 注册验证窗口关闭回调
        self.register_windows_verification_window_callbacks(
            &browser_window,
            &session_id,
            account_id,
        )
        .await?;

        Ok(())
    }

    /// 标准平台：处理账号验证
    async fn handle_account_verification_standard(
        &self,
        account_id: &str,
        account: &crate::models::account::Account,
    ) -> Result<(), String> {
        println!("🍎 标准平台：处理账号验证: account_id={}", account_id);

        // 重用现有会话ID更新账号Cookie
        let session_id = self
            .account_service
            .reuse_login_session_for_account(account_id)
            .await?;

        // 获取会话信息
        let session = self
            .account_service
            .get_login_session(&session_id)
            .await
            .ok_or_else(|| "无法获取登录会话".to_string())?;

        // 打开验证页面（与Cookie更新窗口完全一致）
        let verification_url = "https://www.goofish.com/search?q=相机";
        let user_data_path = session.data_directory.to_string_lossy().to_string();

        let window_config = crate::services::browser::BrowserWindowConfig::default()
            .with_title(&format!(
                "账号验证 - {} - 请完成验证后关闭窗口",
                account.name
            ))
            .with_size(1200.0, 800.0)
            .with_user_data_path(user_data_path.clone());

        // 使用session_id作为窗口标识（与Cookie更新窗口一致）
        let window_label = format!("goofish_verification_{}", session_id);

        let (browser_window, _) = self
            .browser_service
            .open_browser(
                verification_url,
                Some(window_label.clone()),
                Some(window_config),
            )
            .await?;

        println!("✅ 标准平台验证窗口已打开: {}", browser_window.label());

        // 延迟注册窗口关闭回调（与Cookie更新窗口一致）
        let browser_window_for_callback = browser_window.clone();
        let self_clone = self.clone();
        let session_id_clone = session_id.clone();
        let account_id_clone = account_id.to_string();
        tokio::spawn(async move {
            // 等待窗口完全加载
            tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;

            if let Err(e) = self_clone
                .register_verification_window_callbacks(
                    &browser_window_for_callback,
                    &session_id_clone,
                    &account_id_clone,
                )
                .await
            {
                eprintln!("注册验证窗口回调失败: {}", e);
            }
        });

        Ok(())
    }

    /// 处理账号验证完成（现在验证窗口会自动更新Cookie）
    pub async fn handle_account_verification_completed(
        &self,
        account_id: &str,
    ) -> Result<(), String> {
        println!("✅ 处理账号验证完成: account_id={}", account_id);

        self.logging_service
            .business_info(
                "GoldfishBusiness",
                "handle_account_verification_completed",
                &format!("账号验证完成: account_id={}", account_id),
            )
            .await?;

        // 由于验证窗口已经自动更新了Cookie，这里直接验证Cookie有效性
        let is_valid = self
            .account_service
            .validate_account_cookie(account_id)
            .await?;

        if is_valid {
            // Cookie有效，恢复监控
            self.monitor_service
                .resume_account_after_verification(account_id)
                .await?;

            self.logging_service
                .business_info(
                    "GoldfishBusiness",
                    "handle_account_verification_completed",
                    &format!("账号 {} Cookie验证成功，已恢复监控", account_id),
                )
                .await?;

            println!("✅ 账号 {} 验证成功，监控已恢复", account_id);
        } else {
            self.logging_service
                .business_warn(
                    "GoldfishBusiness",
                    "handle_account_verification_completed",
                    &format!("账号 {} Cookie验证失败，保持暂停状态", account_id),
                )
                .await?;

            println!("❌ 账号 {} Cookie验证失败，保持暂停状态", account_id);
        }

        Ok(())
    }

    /// Windows 专用：注册验证窗口关闭回调
    async fn register_windows_verification_window_callbacks(
        &self,
        browser_window: &crate::services::browser::BrowserWindow,
        session_id: &str,
        account_id: &str,
    ) -> Result<(), String> {
        let window_label = browser_window.label().to_string();
        println!("🪟 注册 Windows 验证窗口关闭回调: {}", window_label);

        let self_clone = self.clone();
        let session_id_clone = session_id.to_string();
        let account_id_clone = account_id.to_string();

        // 注册窗口关闭回调
        let callback_config = crate::utils::window_callback_pool::CallbackConfig {
            prevent_close: false, // 允许用户直接关闭窗口
            description: "Windows 验证窗口回调".to_string(),
            execute_once: true, // 只执行一次
        };

        crate::utils::get_window_callback_pool()
            .add_callback(
                &window_label,
                move |_window, _event| {
                    let self_clone = self_clone.clone();
                    let session_id_clone = session_id_clone.clone();
                    let account_id_clone = account_id_clone.clone();
                    Box::pin(async move {
                        println!(
                            "🪟 Windows 验证窗口关闭，处理账号验证完成: {}",
                            account_id_clone
                        );

                        // 处理验证完成
                        if let Err(e) = self_clone
                            .handle_account_verification_completed(&account_id_clone)
                            .await
                        {
                            println!("⚠️ 处理账号验证完成失败: {}", e);
                        }

                        // 清理 Windows 会话
                        if let Some(windows_service) =
                            self_clone.account_service.get_windows_service()
                        {
                            if let Err(e) = windows_service
                                .cleanup_windows_login_session(&session_id_clone)
                                .await
                            {
                                println!("⚠️ 清理 Windows 验证会话失败: {}", e);
                            }
                        }

                        Ok(crate::utils::window_callback_pool::CallbackResult::Continue)
                    })
                },
                callback_config,
            )
            .await;

        println!("✅ 已注册 Windows 验证窗口关闭回调: {}", window_label);
        Ok(())
    }

    /// 标准平台：注册验证窗口关闭回调（与Cookie更新窗口完全一致）
    async fn register_verification_window_callbacks(
        &self,
        browser_window: &crate::services::browser::BrowserWindow,
        session_id: &str,
        account_id: &str,
    ) -> Result<(), String> {
        let window_label = browser_window.label();
        let account_service = self.account_service.clone();
        let logging_service = self.logging_service.clone();
        let session_id = session_id.to_string();
        let account_id = account_id.to_string();

        // 注册关闭请求回调（与Cookie更新窗口完全一致）
        crate::utils::add_close_requested_callback(
            window_label,
            move |window| {
                let account_service = account_service.clone();
                let logging_service = logging_service.clone();
                let session_id = session_id.clone();
                let account_id = account_id.clone();
                let window = window.clone();

                Box::pin(async move {
                    // 检查是否是验证窗口
                    if !window.label().starts_with("goofish_verification_") {
                        return Ok(crate::utils::CallbackResult::Continue);
                    }

                    if let Err(e) = logging_service
                        .business_info(
                            "GoldfishBusiness",
                            "verification_close_requested",
                            &format!(
                                "验证窗口 {} 请求关闭，开始提取Cookie (会话ID: {}, 账号ID: {})",
                                window.label(),
                                session_id,
                                account_id
                            ),
                        )
                        .await
                    {
                        eprintln!("日志记录失败: {}", e);
                    }

                    // 提取Cookie（与Cookie更新窗口完全一致）
                    let cookies_result = window.cookies();
                    match cookies_result {
                        Ok(cookies) => {
                            println!("🍪 验证窗口Cookie提取成功，数量: {}", cookies.len());

                            // 查找闲鱼相关的Cookie
                            let mut goofish_cookies = Vec::new();
                            for cookie in cookies {
                                if let Some(domain) = cookie.domain() {
                                    if domain.contains("goofish.com")
                                        || domain.contains("taobao.com")
                                    {
                                        goofish_cookies.push(cookie);
                                    }
                                }
                            }

                            if !goofish_cookies.is_empty() {
                                // 构建Cookie字符串
                                let cookie_string = goofish_cookies
                                    .iter()
                                    .map(|c| format!("{}={}", c.name(), c.value()))
                                    .collect::<Vec<_>>()
                                    .join("; ");

                                println!("🔄 验证窗口提取到闲鱼Cookie，开始更新账号");

                                // 更新账号Cookie
                                match account_service
                                    .update_account_cookie(&account_id, cookie_string)
                                    .await
                                {
                                    Ok(_) => {
                                        if let Err(e) = logging_service
                                            .business_info(
                                                "GoldfishBusiness",
                                                "verification_close_requested",
                                                &format!(
                                                    "验证窗口Cookie更新成功: 账号ID {}",
                                                    account_id
                                                ),
                                            )
                                            .await
                                        {
                                            eprintln!("日志记录失败: {}", e);
                                        }
                                        println!("✅ 验证窗口Cookie更新成功");
                                    }
                                    Err(e) => {
                                        if let Err(log_err) = logging_service
                                            .business_info(
                                                "GoldfishBusiness",
                                                "verification_close_requested",
                                                &format!("验证窗口Cookie更新失败: {}", e),
                                            )
                                            .await
                                        {
                                            eprintln!("日志记录失败: {}", log_err);
                                        }
                                        println!("❌ 验证窗口Cookie更新失败: {}", e);
                                    }
                                }
                            } else {
                                if let Err(e) = logging_service
                                    .business_info(
                                        "GoldfishBusiness",
                                        "verification_close_requested",
                                        "验证窗口未找到任何Cookie",
                                    )
                                    .await
                                {
                                    eprintln!("日志记录失败: {}", e);
                                }
                                println!("⚠️ 验证窗口未找到Cookie，但保留会话以便下次重用");
                            }
                        }
                        Err(e) => {
                            if let Err(log_err) = logging_service
                                .business_info(
                                    "GoldfishBusiness",
                                    "verification_close_requested",
                                    &format!("验证窗口Cookie API 调用失败: {}", e),
                                )
                                .await
                            {
                                eprintln!("日志记录失败: {}", log_err);
                            }
                            println!("⚠️ 验证窗口Cookie提取失败，但保留会话以便下次重用");
                        }
                    }

                    // 异步处理完成后手动关闭窗口
                    if let Err(e) = window.close() {
                        eprintln!("手动关闭验证窗口失败: {}", e);
                    }

                    Ok(crate::utils::CallbackResult::PreventAndHandle)
                })
            },
            true, // 阻止关闭
            "验证窗口回调",
        )
        .await;

        println!("✅ 已注册标准平台验证窗口关闭回调: {}", window_label);
        Ok(())
    }
}
