// 新的重构架构 - 完全解耦的服务化设计
mod business;
mod core;
mod models;
mod services;
mod utils;

use anyhow::Result;
use business::AppController;
use services::*;
use tauri::{Emitter, Manager};
use tracing::{error, info};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

/// 初始化日志系统
fn init_logging() -> Result<()> {
    // 强制输出到控制台，确保在开发时能看到日志
    let filter = tracing_subscriber::EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| "goldfish_app=debug,info".into());

    // 使用简单的格式化器，直接输出到stderr（控制台）
    tracing_subscriber::fmt()
        .with_env_filter(filter)
        .with_timer(tracing_subscriber::fmt::time::ChronoLocal::new(
            "%H:%M:%S".to_string(),
        ))
        .with_target(false)
        .with_thread_ids(false)
        .with_thread_names(false)
        .with_file(false)
        .with_line_number(false)
        .with_writer(std::io::stderr) // 输出到stderr，这样在控制台更容易看到
        .init();

    // 测试日志输出 - 使用eprintln!确保能在控制台看到
    eprintln!("🔧 日志系统已启动 (开发模式: {})", cfg!(debug_assertions));
    tracing::info!("🔧 Tracing日志系统已启动");

    Ok(())
}

/// 应用启动时的初始化
async fn setup_app(app_handle: tauri::AppHandle) -> Result<(), String> {
    info!("🚀 开始应用初始化...");

    // 第一步：创建主窗口，默认显示Loading页面
    info!("📱 正在创建主窗口...");

    let main_window = tauri::WebviewWindowBuilder::new(
        &app_handle,
        "main",
        tauri::WebviewUrl::App("index.html#/loading".into()),
    )
    .title("Goldfish Monitor - 加载中")
    .inner_size(900.0, 800.0)
    .center()
    .resizable(false)
    .decorations(false)
    // .always_on_top(true)
    .build()
    .map_err(|e| {
        let error_msg = format!("创建主窗口失败: {}", e);
        error!("{}", error_msg);
        error_msg
    })?;

    info!("✅ 主窗口创建成功，显示Loading页面");

    // 第二步：初始化服务
    app_handle.emit("loading_status", "正在初始化服务...").ok();

    // 创建应用控制器
    let app_controller = AppController::new(app_handle.clone()).await?;

    // 将服务注册到Tauri状态管理
    app_handle.manage(app_controller.logging_service().clone());
    app_handle.manage(app_controller.config_service().clone());
    app_handle.manage(app_controller.auth_service().clone());
    app_handle.manage(app_controller.browser_service().clone());
    app_handle.manage(app_controller.monitor_service().clone());
    app_handle.manage(app_controller.notification_service().clone());
    app_handle.manage(app_controller.account_service().clone());
    app_handle.manage(app_controller.time_service().clone());
    app_handle.manage(app_controller.goldfish_business().clone());
    app_handle.manage(app_controller.clone());

    info!("✅ 服务初始化完成");

    // 第三步：校验激活状态
    app_handle
        .emit("loading_status", "正在校验激活状态...")
        .ok();

    let activation_result = app_controller
        .auth_service()
        .check_activation_status()
        .await;
    let is_activated = activation_result.unwrap_or(false);

    info!("🔐 激活状态校验完成: {}", is_activated);

    // 第四步：根据激活状态跳转到对应页面
    tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await; // 让用户看到加载过程

    if is_activated {
        // 激活成功 - 跳转到主页面
        info!("✅ 应用已激活，配置主界面...");
        app_handle
            .emit("loading_status", "激活成功，正在加载主界面...")
            .ok();

        // 设置主应用窗口大小和属性
        main_window
            .set_size(tauri::LogicalSize::new(1366.0, 768.0))
            .ok();
        main_window.set_resizable(true).ok();
        main_window.set_decorations(true).ok();
        main_window.set_always_on_top(false).ok();
        main_window.set_title("Goldfish Monitor - 闲鱼监控").ok();

        // 初始化应用业务逻辑
        info!("🚀 初始化应用业务逻辑...");
        app_controller.initialize_app().await?;

        // 发送激活成功事件，前端路由会自动跳转到主页
        info!("📡 发送激活成功事件到前端...");
        app_handle.emit("app_activation_success", ()).ok();

        info!("✅ 激活成功，已跳转到主页面");
    } else {
        // 未激活 - 跳转到激活页面
        info!("🔐 应用未激活，配置激活界面...");
        app_handle
            .emit("loading_status", "需要激活，正在加载激活界面...")
            .ok();

        // 设置激活页面窗口大小和属性
        main_window
            .set_size(tauri::LogicalSize::new(900.0, 800.0))
            .ok();
        main_window.set_resizable(true).ok();
        main_window.set_decorations(true).ok();
        main_window.set_always_on_top(false).ok();
        main_window.set_title("Goldfish Monitor - 激活应用").ok();

        // 发送需要激活事件，前端路由会自动跳转到激活页面
        info!("📡 发送需要激活事件到前端...");
        app_handle.emit("app_activation_required", ()).ok();

        info!("✅ 未激活，已跳转到激活页面");
    }

    info!("✅ 应用初始化完成");
    Ok(())
}

/// 打开开发者工具
#[tauri::command]
async fn open_devtools(app_handle: tauri::AppHandle) -> Result<(), String> {
    info!("🔧 打开开发者工具");

    if let Some(window) = app_handle.get_webview_window("main") {
        #[cfg(debug_assertions)]
        window.open_devtools();

        #[cfg(not(debug_assertions))]
        {
            // 生产环境也允许打开开发者工具用于调试
            window.open_devtools();
        }

        info!("✅ 开发者工具已打开");
        Ok(())
    } else {
        let error_msg = "未找到主窗口".to_string();
        error!("{}", error_msg);
        Err(error_msg)
    }
}

/// 应用关闭时的清理
async fn cleanup_app(app_handle: tauri::AppHandle) -> Result<(), String> {
    info!("🧹 开始应用清理...");

    if let Some(app_controller) = app_handle.try_state::<AppController>() {
        app_controller.cleanup_app().await?;
    }

    info!("✅ 应用清理完成");
    Ok(())
}

/// 业务命令 - 启动监控
#[tauri::command]
async fn app_start_monitoring(
    app_controller: tauri::State<'_, AppController>,
) -> Result<(), String> {
    app_controller
        .goldfish_business()
        .start_goldfish_monitoring()
        .await
}

/// 业务命令 - 停止监控
#[tauri::command]
async fn app_stop_monitoring(
    app_controller: tauri::State<'_, AppController>,
) -> Result<(), String> {
    app_controller
        .goldfish_business()
        .stop_goldfish_monitoring()
        .await
}

/// 业务命令 - 检查监控状态
#[tauri::command]
async fn app_is_running(app_controller: tauri::State<'_, AppController>) -> Result<bool, String> {
    Ok(app_controller.monitor_service().is_running().await)
}

/// 业务命令 - 处理登录
#[tauri::command]
async fn app_handle_login(app_controller: tauri::State<'_, AppController>) -> Result<(), String> {
    app_controller
        .goldfish_business()
        .handle_goldfish_login()
        .await
}

/// 业务命令 - 带ID的登录（用于自动添加账号）
#[tauri::command]
async fn app_handle_login_with_id(
    app_controller: tauri::State<'_, AppController>,
    loginId: String,
    description: Option<String>,
) -> Result<(), String> {
    app_controller
        .goldfish_business()
        .handle_goldfish_login_with_id(&loginId, description)
        .await
}

/// 业务命令 - 获取应用状态
#[tauri::command]
async fn app_get_status(
    app_controller: tauri::State<'_, AppController>,
) -> Result<serde_json::Value, String> {
    app_controller.get_app_status().await
}

/// 激活命令 - 激活成功后跳转到主页面
#[tauri::command]
async fn app_activation_success(
    app_handle: tauri::AppHandle,
    app_controller: tauri::State<'_, AppController>,
) -> Result<(), String> {
    info!("🎉 激活成功，正在初始化应用");

    // 获取主窗口
    if let Some(main_window) = app_handle.get_webview_window("main") {
        // 设置主应用窗口大小和属性
        main_window
            .set_size(tauri::LogicalSize::new(1366.0, 768.0))
            .ok();
        main_window.set_resizable(true).ok();
        main_window.set_decorations(true).ok();
        main_window.set_always_on_top(false).ok();
        main_window.set_title("Goldfish Monitor - 闲鱼监控").ok();
    }

    // 初始化应用业务逻辑
    app_controller.initialize_app().await?;

    // 发送激活成功事件，前端路由会处理页面跳转
    app_handle.emit("app_activation_completed", ()).ok();

    info!("✅ 激活流程完成");
    Ok(())
}

/// 激活命令 - 重新检查激活状态
#[tauri::command]
async fn app_recheck_activation(
    app_handle: tauri::AppHandle,
    app_controller: tauri::State<'_, AppController>,
) -> Result<bool, String> {
    info!("🔄 重新检查激活状态");

    let is_activated = app_controller
        .auth_service()
        .check_activation_status()
        .await
        .unwrap_or(false);

    if is_activated {
        // 如果激活成功，自动创建主窗口
        app_activation_success(app_handle, app_controller).await?;
    }

    Ok(is_activated)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志系统
    if let Err(e) = init_logging() {
        eprintln!("日志系统初始化失败: {}", e);
    }

    info!("🐠 Goldfish Monitor 启动中...");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            // 新的业务命令
            app_start_monitoring,
            app_stop_monitoring,
            app_is_running,
            app_handle_login,
            app_handle_login_with_id,
            app_get_status, // 激活流程命令
            app_activation_success,
            app_recheck_activation, // 配置服务命令
            config_get,
            config_update,
            config_reload,
            config_get_file_info,
            config_backup,
            config_restore,
            config_reset_default,
            config_validate, // 日志服务命令
            logs_get,
            logs_clear,
            logs_export,
            logs_get_file_info,
            logs_rotate, // 浏览器服务命令
            browser_open,
            browser_open_with_config,
            browser_close,
            browser_close_all,
            browser_get_active,
            browser_window_exists,
            browser_focus_window,
            browser_minimize_window,
            browser_maximize_window,
            browser_resize_window, // 认证服务命令
            auth_get_device_info,
            auth_generate_device_fingerprint,
            auth_validate_activation_code,
            auth_check_activation_status,
            auth_get_activation_info,
            auth_set_cookie,
            auth_get_cookie,
            auth_clear_cookie,
            auth_clear_all_cookies,
            auth_has_cookie,
            auth_get_all_cookies, // 监控服务命令
            monitor_start,
            monitor_stop,
            monitor_is_running,
            monitor_get_status,
            monitor_get_data,
            monitor_clear_data,
            monitor_update_config,
            monitor_get_config,
            monitor_pause_account,
            monitor_resume_account,
            // 通知服务命令
            notification_add_channel,
            notification_send,
            notification_broadcast,
            notification_test_channel,
            notification_get_channels,
            notification_remove_channel,
            notification_get_dingtalk_status,
            notification_reset_dingtalk_limits,
            test_all_dingtalk_hooks,
            notification_send_persistent,
            notification_dismiss_persistent,
            // 账号管理命令
            account_get_all,
            account_get,
            account_add,
            account_add_via_login,
            account_remove,
            account_update,
            account_get_user_info,
            account_add_with_user_info,
            account_update_cookie,
            account_auto_update_cookie,
            account_validate_cookie,
            account_validate_all,
            account_handle_verification,
            account_verification_completed,
            account_get_next_available,
            account_record_usage,
            account_get_config,
            account_get_stats,
            account_cleanup_failed,
            account_validate_all_cookies,
            account_set_enabled,
            account_set_priority,
            account_get_available_count,
            account_get_enabled_count,
            account_reset_stats,
            account_export_config,
            account_import_config,
            account_test_connection,
            account_get_rotation_status,
            // 时间服务命令
            time_get_info,
            time_sync_from_header,
            time_get_server_time,
            time_get_offset,
            time_validate,
            // 调试命令
            open_devtools,
        ])
        .setup(|app| {
            info!("🔧 Tauri setup 函数开始执行");

            let app_handle = app.handle().clone();

            // 异步初始化
            info!("🚀 准备启动异步初始化任务");

            tauri::async_runtime::spawn(async move {
                info!("🎯 异步初始化任务开始执行");

                if let Err(e) = setup_app(app_handle.clone()).await {
                    error!("应用初始化失败: {}", e);
                    app_handle.emit("app_init_error", e).ok();
                } else {
                    info!("✅ 应用初始化成功");
                    app_handle.emit("app_init_success", ()).ok();
                }
            });

            info!("🔧 Tauri setup 函数执行完成");
            Ok(())
        })
        .on_window_event(|window, event| {
            match event {
                tauri::WindowEvent::CloseRequested { api, .. } => {
                    // 先检查是否需要阻止关闭
                    if let Some(webview_window) =
                        window.app_handle().get_webview_window(window.label())
                    {
                        let pool = crate::utils::get_window_callback_pool();

                        // 同步检查是否有回调要求阻止关闭
                        let should_prevent = pool.should_prevent_close_sync(window.label());
                        println!(
                            "🔍 窗口 {} 关闭请求，是否阻止: {}",
                            window.label(),
                            should_prevent
                        );

                        if should_prevent {
                            println!("🔒 立即阻止窗口 {} 关闭", window.label());
                            api.prevent_close();

                            // 异步执行回调
                            let window = window.clone();
                            let event = event.clone();
                            tauri::async_runtime::spawn(async move {
                                if let Some(webview_window) =
                                    window.app_handle().get_webview_window(window.label())
                                {
                                    let pool = crate::utils::get_window_callback_pool();
                                    if let Err(e) =
                                        pool.execute_callbacks(&webview_window, &event).await
                                    {
                                        eprintln!("执行窗口回调失败: {}", e);
                                    }
                                }
                            });
                            return;
                        }
                    }

                    // 如果不需要阻止关闭，执行默认清理逻辑
                    let app_handle = window.app_handle().clone();
                    tauri::async_runtime::spawn(async move {
                        if let Err(e) = cleanup_app(app_handle).await {
                            error!("应用清理失败: {}", e);
                        }
                    });
                }
                _ => {
                    // 其他事件的通用处理
                    let window = window.clone();
                    let event = event.clone();
                    tauri::async_runtime::spawn(async move {
                        // 将 Window 转换为 WebviewWindow
                        if let Some(webview_window) =
                            window.app_handle().get_webview_window(window.label())
                        {
                            let pool = crate::utils::get_window_callback_pool();
                            if let Err(e) = pool.execute_callbacks(&webview_window, &event).await {
                                eprintln!("执行窗口回调失败: {}", e);
                            }
                        }
                    });
                }
            }
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
