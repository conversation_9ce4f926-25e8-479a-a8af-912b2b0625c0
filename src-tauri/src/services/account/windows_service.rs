use crate::models::account::{Account, AccountC<PERSON>ie, AccountStatus};
use crate::services::account::service::{LoginSession, LoginSessionStatus, UserInfo};
use crate::services::browser::BrowserWindowConfig;
use crate::services::crypto::EncryptedClient;
use crate::services::time::TimeService;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use std::sync::Arc;
use tauri::{AppHandle, Emitter, Manager};
use tokio::sync::RwLock;
use uuid::Uuid;

/// Windows 专用登录会话信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowsLoginSession {
    pub session_id: String,
    pub data_directory: PathBuf,
    pub description: Option<String>,
    pub created_at: chrono::DateTime<Utc>,
    pub status: LoginSessionStatus,
    pub process_isolation_id: String, // Windows 专用：进程隔离标识符
    pub user_data_suffix: String,     // Windows 专用：用户数据目录后缀
}

/// Windows 专用账号服务
/// 专门处理 Windows 平台的账号数据隔离问题
#[derive(Clone)]
pub struct WindowsAccountService {
    app_handle: AppHandle,
    login_sessions: Arc<RwLock<HashMap<String, WindowsLoginSession>>>,
    data_dir: PathBuf,
    time_service: TimeService,
}

impl WindowsAccountService {
    /// 创建新的 Windows 专用账号服务实例
    pub fn new(app_handle: AppHandle, app_data_dir: PathBuf, time_service: TimeService) -> Self {
        let data_dir = app_data_dir;
        println!(
            "🪟 Windows 专用账号服务初始化，数据目录: {}",
            data_dir.display()
        );

        Self {
            app_handle,
            login_sessions: Arc::new(RwLock::new(HashMap::new())),
            data_dir,
            time_service,
        }
    }

    /// 生成 Windows 专用的进程隔离标识符
    /// 使用时间戳和随机数确保每个会话完全独立
    fn generate_process_isolation_id() -> String {
        let timestamp = chrono::Utc::now().timestamp_millis();
        let random_id = Uuid::new_v4().to_string().replace("-", "");
        format!("win_{}_{}", timestamp, &random_id[0..8])
    }

    /// 生成 Windows 专用的用户数据目录后缀
    fn generate_user_data_suffix(session_id: &str) -> String {
        use sha2::{Digest, Sha256};

        let mut hasher = Sha256::new();
        hasher.update(session_id.as_bytes());
        hasher.update(chrono::Utc::now().timestamp().to_string().as_bytes());
        let hash = hasher.finalize();

        // 使用哈希的前8字节作为后缀，确保目录名唯一
        format!("goldfish_win_{}", hex::encode(&hash[0..8]))
    }

    /// 创建 Windows 专用登录会话
    pub async fn create_windows_login_session(
        &self,
        description: Option<String>,
    ) -> Result<String, String> {
        let session_id = Uuid::new_v4().to_string();
        let process_isolation_id = Self::generate_process_isolation_id();
        let user_data_suffix = Self::generate_user_data_suffix(&session_id);

        // Windows 专用：创建完全独立的数据目录
        let session_dir = self
            .data_dir
            .join("windows_sessions")
            .join(&user_data_suffix);

        // 创建会话目录
        if let Err(e) = fs::create_dir_all(&session_dir) {
            return Err(format!("创建 Windows 会话目录失败: {}", e));
        }

        // 确保使用绝对路径
        let absolute_session_dir = session_dir
            .canonicalize()
            .unwrap_or_else(|_| session_dir.clone());

        println!(
            "🪟 创建 Windows 专用会话目录: {}",
            absolute_session_dir.display()
        );
        println!("🔑 进程隔离ID: {}", process_isolation_id);
        println!("📁 用户数据后缀: {}", user_data_suffix);

        let session = WindowsLoginSession {
            session_id: session_id.clone(),
            data_directory: absolute_session_dir,
            description,
            created_at: Utc::now(),
            status: LoginSessionStatus::Active,
            process_isolation_id,
            user_data_suffix,
        };

        // 保存会话信息
        {
            let mut sessions = self.login_sessions.write().await;
            sessions.insert(session_id.clone(), session);
        }

        println!("✅ Windows 专用登录会话创建成功: {}", session_id);
        Ok(session_id)
    }

    /// 获取 Windows 登录会话
    pub async fn get_windows_login_session(&self, session_id: &str) -> Option<WindowsLoginSession> {
        let sessions = self.login_sessions.read().await;
        sessions.get(session_id).cloned()
    }

    /// 更新 Windows 登录会话状态
    pub async fn update_windows_login_session_status(
        &self,
        session_id: &str,
        status: LoginSessionStatus,
    ) -> Result<(), String> {
        let mut sessions = self.login_sessions.write().await;
        if let Some(session) = sessions.get_mut(session_id) {
            session.status = status;
            println!(
                "🪟 更新 Windows 登录会话状态: {} -> {:?}",
                session_id, session.status
            );
            Ok(())
        } else {
            Err(format!("Windows 登录会话不存在: {}", session_id))
        }
    }

    /// 清理 Windows 登录会话
    pub async fn cleanup_windows_login_session(&self, session_id: &str) -> Result<(), String> {
        let session = {
            let mut sessions = self.login_sessions.write().await;
            sessions.remove(session_id)
        };

        if let Some(session) = session {
            println!("🪟 开始清理 Windows 登录会话: {}", session_id);

            // 删除会话目录
            if session.data_directory.exists() {
                if let Err(e) = fs::remove_dir_all(&session.data_directory) {
                    println!("⚠️ 删除 Windows 会话目录失败: {}", e);
                } else {
                    println!("🗑️ 已删除 Windows 会话目录: {:?}", session.data_directory);
                }
            }

            println!("✅ Windows 登录会话清理完成: {}", session_id);
        }

        Ok(())
    }

    /// Windows 专用：处理登录会话完成（带Cookie）
    /// 注意：这个方法只创建账号对象，不保存到配置。需要调用者自行保存。
    pub async fn handle_windows_login_session_completed_with_cookie(
        &self,
        session_id: &str,
        cookie_string: String,
    ) -> Result<(Account, bool), String> {
        println!("🪟 处理 Windows 登录会话完成（带Cookie）: {}", session_id);

        // 更新会话状态
        self.update_windows_login_session_status(session_id, LoginSessionStatus::Completed)
            .await?;

        // 验证Cookie并获取用户信息
        let user_info = self.get_user_info(&cookie_string).await?;

        if !user_info.is_valid {
            self.update_windows_login_session_status(session_id, LoginSessionStatus::Failed)
                .await?;
            self.cleanup_windows_login_session(session_id).await?;
            return Err("Cookie验证失败".to_string());
        }

        println!("✅ Windows Cookie验证成功，创建账号");

        // 获取会话描述
        let description = self
            .get_windows_login_session(session_id)
            .await
            .and_then(|s| s.description);

        // 创建账号
        let mut account = Account::new(
            user_info
                .nickname
                .clone()
                .unwrap_or_else(|| "未知用户".to_string()),
        );

        if let Some(desc) = description {
            account.description = Some(desc);
        }

        // 设置Cookie
        let account_cookie = AccountCookie::new(cookie_string, "goofish.com".to_string());
        account.set_cookie(account_cookie);

        // 保存用户信息到metadata
        if let Some(uid) = user_info.user_id {
            account.metadata.insert("user_id".to_string(), uid);
        }
        if let Some(nick) = user_info.nickname {
            account.name = nick.clone();
            account.metadata.insert("nickname".to_string(), nick);
        }
        if let Some(avatar_url) = user_info.avatar {
            account.metadata.insert("avatar".to_string(), avatar_url);
        }

        // Windows 专用：保存会话信息到账号metadata
        if let Some(session) = self.get_windows_login_session(session_id).await {
            account
                .metadata
                .insert("windows_session_id".to_string(), session_id.to_string());
            account.metadata.insert(
                "windows_process_isolation_id".to_string(),
                session.process_isolation_id,
            );
            account.metadata.insert(
                "windows_user_data_suffix".to_string(),
                session.user_data_suffix,
            );
            account.metadata.insert(
                "windows_data_directory".to_string(),
                session.data_directory.to_string_lossy().to_string(),
            );

            println!("💾 已保存 Windows 专用会话信息到账号metadata");
        }

        // 设置为活跃状态
        account.update_status(AccountStatus::Active);

        self.update_windows_login_session_status(session_id, LoginSessionStatus::Verified)
            .await?;

        println!(
            "✅ Windows 账号对象创建成功: {} (ID: {})",
            account.name, account.id
        );
        Ok((account, false)) // 返回新添加标志
    }

    /// 获取用户信息（验证Cookie）
    pub async fn get_user_info(&self, cookie: &str) -> Result<UserInfo, String> {
        println!("🔍 开始获取用户信息");

        // 创建加密客户端
        let client = EncryptedClient::new().map_err(|e| format!("创建加密客户端失败: {}", e))?;

        // 构建请求URL和数据
        let url = "https://h5api.m.goofish.com/h5/mtop.idle.web.user.page.head/1.0/";
        let request_data = serde_json::json!({"self": true});

        println!("🌐 发送用户信息请求");

        // 使用加密客户端发送请求（获取响应头以提取服务器时间）
        match client
            .post_encrypted_with_headers(url, &request_data, cookie)
            .await
        {
            Ok((response, headers)) => {
                // 提取服务器时间并同步
                if let Some(date_header) = headers.get("date") {
                    if let Ok(date_str) = date_header.to_str() {
                        println!("🕒 检测到服务器时间: {}", date_str);
                        if let Err(e) = self
                            .time_service
                            .update_from_response_header(date_str)
                            .await
                        {
                            println!("⚠️ 服务器时间同步失败: {}", e);
                        }
                    }
                }

                // 解析响应获取用户信息
                if let Some(data) = response.get("data") {
                    // 检查是否有baseInfo（表示请求成功）
                    if let Some(base_info) = data.get("baseInfo") {
                        // 获取用户ID
                        let user_id = base_info
                            .get("kcUserId")
                            .and_then(|v| v.as_str())
                            .map(|s| s.to_string());

                        // 获取昵称和头像
                        let mut nickname = None;
                        let mut avatar = None;

                        if let Some(module) = data.get("module") {
                            if let Some(base) = module.get("base") {
                                // 获取昵称
                                nickname = base
                                    .get("displayName")
                                    .and_then(|v| v.as_str())
                                    .map(|s| s.to_string());

                                // 获取头像
                                if let Some(avatar_obj) = base.get("avatar") {
                                    avatar = avatar_obj
                                        .get("avatar")
                                        .and_then(|v| v.as_str())
                                        .map(|s| s.to_string());
                                }
                            }
                        }

                        println!(
                            "✅ 获取用户信息成功: ID={:?}, 昵称={:?}, 头像={:?}",
                            user_id, nickname, avatar
                        );

                        return Ok(UserInfo {
                            user_id,
                            nickname,
                            avatar,
                            is_valid: true,
                        });
                    }
                }

                // 如果没有找到有效的用户信息，返回无效状态
                println!("❌ 响应中没有找到有效的用户信息");
                Ok(UserInfo {
                    user_id: None,
                    nickname: None,
                    avatar: None,
                    is_valid: false,
                })
            }
            Err(e) => {
                println!("❌ 获取用户信息失败: {}", e);
                Err(format!("获取用户信息失败: {}", e))
            }
        }
    }

    /// Windows 专用：重用现有会话ID更新账号Cookie
    pub async fn reuse_windows_login_session_for_account(
        &self,
        account: &Account,
    ) -> Result<String, String> {
        println!("🪟 重用 Windows 登录会话更新账号Cookie: {}", account.id);

        // 检查是否有保存的 Windows 会话信息
        if let Some(session_id) = account.metadata.get("windows_session_id") {
            println!("🔍 找到保存的 Windows 会话ID: {}", session_id);

            // 获取保存的会话信息
            if let (Some(process_isolation_id), Some(user_data_suffix)) = (
                account.metadata.get("windows_process_isolation_id"),
                account.metadata.get("windows_user_data_suffix"),
            ) {
                // 重新创建会话目录
                let session_dir = self
                    .data_dir
                    .join("windows_sessions")
                    .join(user_data_suffix);

                // 确保会话目录存在
                if let Err(e) = fs::create_dir_all(&session_dir) {
                    println!("⚠️ 重新创建 Windows 会话目录失败: {}", e);
                } else {
                    println!("✅ Windows 会话目录已确保存在，重用会话ID: {}", session_id);
                }

                // 确保使用绝对路径
                let absolute_session_dir = session_dir
                    .canonicalize()
                    .unwrap_or_else(|_| session_dir.clone());

                // 重新激活会话
                let session = WindowsLoginSession {
                    session_id: session_id.clone(),
                    data_directory: absolute_session_dir,
                    description: Some(format!("更新账号 {} 的Cookie", account.name)),
                    created_at: chrono::Utc::now(),
                    status: LoginSessionStatus::Active,
                    process_isolation_id: process_isolation_id.clone(),
                    user_data_suffix: user_data_suffix.clone(),
                };

                // 保存会话信息
                {
                    let mut sessions = self.login_sessions.write().await;
                    sessions.insert(session_id.clone(), session);
                }

                println!("✅ 重用现有 Windows 会话: {}", session_id);
                return Ok(session_id.clone());
            }
        }

        // 如果没有现有会话或会话信息不完整，创建新会话
        let new_session_id = self
            .create_windows_login_session(Some(format!("更新账号 {} 的Cookie", account.name)))
            .await?;

        println!("✅ 已为账号创建新的 Windows 登录会话: {}", new_session_id);
        Ok(new_session_id)
    }
}
