use crate::services::account::windows_service::WindowsLoginSession;
use crate::services::browser::service::BrowserWindowInfo;
use crate::services::browser::{BrowserWindow, BrowserWindowConfig};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Manager, WebviewUrl, WebviewWindow, WebviewWindowBuilder};
use tokio::sync::Mutex;

/// Windows 专用浏览器窗口配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowsBrowserConfig {
    pub base_config: BrowserWindowConfig,
    pub process_isolation_id: String,
    pub user_data_suffix: String,
    pub force_new_process: bool, // Windows 专用：强制新进程
}

impl WindowsBrowserConfig {
    /// 从 Windows 登录会话创建配置
    pub fn from_windows_session(
        session: &WindowsLoginSession,
        base_config: BrowserWindowConfig,
    ) -> Self {
        Self {
            base_config,
            process_isolation_id: session.process_isolation_id.clone(),
            user_data_suffix: session.user_data_suffix.clone(),
            force_new_process: true,
        }
    }

    /// 设置窗口标题
    pub fn with_title(mut self, title: &str) -> Self {
        self.base_config.title = title.to_string();
        self
    }

    /// 设置窗口大小
    pub fn with_size(mut self, width: f64, height: f64) -> Self {
        self.base_config.width = width;
        self.base_config.height = height;
        self
    }
}

/// Windows 专用浏览器服务
/// 专门处理 Windows 平台的浏览器窗口数据隔离
#[derive(Clone)]
pub struct WindowsBrowserService {
    app_handle: AppHandle,
    active_browsers: Arc<Mutex<HashMap<String, BrowserWindowInfo>>>,
}

impl WindowsBrowserService {
    /// 创建新的 Windows 专用浏览器服务实例
    pub fn new(app_handle: AppHandle) -> Self {
        println!("🪟 Windows 专用浏览器服务初始化");
        Self {
            app_handle,
            active_browsers: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// Windows 专用：打开浏览器窗口，使用完全隔离的数据目录
    pub async fn open_windows_browser(
        &self,
        url: &str,
        session: &WindowsLoginSession,
        config: Option<BrowserWindowConfig>,
    ) -> Result<BrowserWindow, String> {
        let base_config = config.unwrap_or_default();
        let windows_config = WindowsBrowserConfig::from_windows_session(session, base_config);

        // 使用会话ID作为窗口标识，确保唯一性
        let window_label = format!("windows_browser_{}", session.session_id);

        println!("🪟 创建 Windows 专用浏览器窗口: {}", window_label);
        println!("🔑 进程隔离ID: {}", windows_config.process_isolation_id);
        println!("📁 用户数据后缀: {}", windows_config.user_data_suffix);

        // 检查窗口是否已存在
        if self.app_handle.get_webview_window(&window_label).is_some() {
            return Err(format!("Windows 浏览器窗口 {} 已存在", window_label));
        }

        // 创建窗口构建器
        let mut builder = WebviewWindowBuilder::new(
            &self.app_handle,
            &window_label,
            WebviewUrl::External(url.parse().map_err(|e| format!("无效的URL: {}", e))?),
        )
        .title(&windows_config.base_config.title)
        .inner_size(
            windows_config.base_config.width,
            windows_config.base_config.height,
        )
        .resizable(windows_config.base_config.resizable)
        .closable(windows_config.base_config.closable)
        .minimizable(windows_config.base_config.minimizable)
        .maximizable(windows_config.base_config.maximizable)
        .decorations(windows_config.base_config.decorations)
        .always_on_top(windows_config.base_config.always_on_top)
        .skip_taskbar(windows_config.base_config.skip_taskbar)
        .fullscreen(windows_config.base_config.fullscreen);

        // Windows 专用：设置完全独立的用户代理
        let custom_user_agent = format!(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 GoldfishWindows/{}/{}",
            windows_config.process_isolation_id,
            windows_config.user_data_suffix
        );
        builder = builder.user_agent(&custom_user_agent);
        println!("🔧 设置 Windows 专用用户代理: {}", custom_user_agent);

        // Windows 专用：使用会话的数据目录路径
        let user_data_path = session.data_directory.to_string_lossy().to_string();
        println!("🗂️ 设置 Windows 浏览器用户数据目录: {}", user_data_path);

        // Windows 专用：生成唯一的数据存储标识符
        let data_store_identifier = self.generate_windows_data_store_identifier(
            &windows_config.process_isolation_id,
            &windows_config.user_data_suffix,
        );

        println!("🔑 Windows 专用数据存储标识符: {:?}", data_store_identifier);
        builder = builder.data_store_identifier(data_store_identifier);

        // 设置位置
        if let (Some(x), Some(y)) = (windows_config.base_config.x, windows_config.base_config.y) {
            builder = builder.position(x, y);
        } else if windows_config.base_config.center {
            builder = builder.center();
        }

        // 创建窗口
        let window = builder
            .build()
            .map_err(|e| format!("创建 Windows 浏览器窗口失败: {}", e))?;

        // 记录窗口信息
        let window_info = BrowserWindowInfo {
            label: window_label.clone(),
            url: url.to_string(),
            title: windows_config.base_config.title.clone(),
            config: windows_config.base_config,
            created_at: chrono::Utc::now().to_rfc3339(),
        };

        {
            let mut browsers = self.active_browsers.lock().await;
            browsers.insert(window_label.clone(), window_info);
        }

        // 发送窗口创建事件
        self.emit_windows_browser_opened(&window_label, url).await?;

        // 创建并返回 BrowserWindow 实例
        let browser_window = BrowserWindow::new(window_label, self.app_handle.clone());
        println!("✅ Windows 专用浏览器窗口创建成功");
        Ok(browser_window)
    }

    /// Windows 专用：生成数据存储标识符
    fn generate_windows_data_store_identifier(
        &self,
        process_isolation_id: &str,
        user_data_suffix: &str,
    ) -> [u8; 16] {
        use sha2::{Digest, Sha256};

        let mut hasher = Sha256::new();
        hasher.update(process_isolation_id.as_bytes());
        hasher.update(user_data_suffix.as_bytes());
        hasher.update(chrono::Utc::now().timestamp().to_string().as_bytes());
        let hash = hasher.finalize();

        // 取前16字节作为标识符
        let mut identifier = [0u8; 16];
        identifier.copy_from_slice(&hash[0..16]);

        identifier
    }

    /// 关闭 Windows 浏览器窗口
    pub async fn close_windows_browser(&self, label: &str) -> Result<(), String> {
        if let Some(window) = self.app_handle.get_webview_window(label) {
            window
                .close()
                .map_err(|e| format!("关闭 Windows 窗口失败: {}", e))?;

            // 从记录中移除
            {
                let mut browsers = self.active_browsers.lock().await;
                browsers.remove(label);
            }

            // 发送窗口关闭事件
            self.emit_windows_browser_closed(label).await?;

            println!("✅ Windows 浏览器窗口已关闭: {}", label);
            Ok(())
        } else {
            Err(format!("Windows 浏览器窗口 {} 不存在", label))
        }
    }

    /// 获取活动的 Windows 浏览器窗口列表
    pub async fn get_active_windows_browsers(&self) -> HashMap<String, BrowserWindowInfo> {
        let browsers = self.active_browsers.lock().await;
        browsers.clone()
    }

    /// 检查 Windows 浏览器窗口是否存在
    pub fn is_windows_browser_exists(&self, label: &str) -> bool {
        self.app_handle.get_webview_window(label).is_some()
    }

    /// 发送 Windows 浏览器打开事件
    async fn emit_windows_browser_opened(&self, label: &str, url: &str) -> Result<(), String> {
        let payload = serde_json::json!({
            "label": label,
            "url": url,
            "action": "opened",
            "platform": "windows",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.app_handle
            .emit("windows_browser_window_event", &payload)
            .map_err(|e| format!("发送 Windows 浏览器打开事件失败: {}", e))
    }

    /// 发送 Windows 浏览器关闭事件
    async fn emit_windows_browser_closed(&self, label: &str) -> Result<(), String> {
        let payload = serde_json::json!({
            "label": label,
            "action": "closed",
            "platform": "windows",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.app_handle
            .emit("windows_browser_window_event", &payload)
            .map_err(|e| format!("发送 Windows 浏览器关闭事件失败: {}", e))
    }
}
